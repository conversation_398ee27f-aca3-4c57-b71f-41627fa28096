"""
<PERSON><PERSON><PERSON> to load and use the fine-tuned PEFT model with base model.
This script demonstrates how to load the model from the saved checkpoint.
"""

import os
import json
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from peft import PeftModel

def load_model_from_checkpoint(checkpoint_path):
    """
    Load PEFT model from checkpoint with base model.
    
    Args:
        checkpoint_path: Path to the checkpoint directory
        
    Returns:
        model: Loaded PEFT model
        tokenizer: Loaded tokenizer
        base_model_info: Information about the base model
    """
    
    # Load base model info
    base_model_info_path = os.path.join(checkpoint_path, "base_model_info.json")
    if os.path.exists(base_model_info_path):
        with open(base_model_info_path, "r") as f:
            base_model_info = json.load(f)
        print(f"Base model info: {base_model_info}")
    else:
        # Fallback to default if info file doesn't exist
        base_model_info = {"base_model_name": "bigscience/bloomz-560m"}
        print("Warning: base_model_info.json not found, using default base model")
    
    # Determine base model path
    base_model_path = base_model_info.get("base_model_path", base_model_info["base_model_name"])
    
    # Load base model
    print(f"Loading base model from: {base_model_path}")
    base_model = AutoModelForCausalLM.from_pretrained(base_model_path)
    
    # Load PEFT model
    print(f"Loading PEFT adapter from: {checkpoint_path}")
    model = PeftModel.from_pretrained(base_model, checkpoint_path)
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)
    
    print("Model loaded successfully!")
    return model, tokenizer, base_model_info

def generate_text(model, tokenizer, prompt, max_length=100, device="cpu"):
    """
    Generate text using the loaded model.
    
    Args:
        model: The loaded PEFT model
        tokenizer: The tokenizer
        prompt: Input prompt
        max_length: Maximum length of generated text
        device: Device to run inference on
        
    Returns:
        generated_text: The generated text
    """
    model.eval()
    model = model.to(device)
    
    # Tokenize input
    inputs = tokenizer(prompt, return_tensors="pt").to(device)
    
    # Generate
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_length=max_length,
            num_return_sequences=1,
            temperature=0.7,
            do_sample=True,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # Decode output
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    return generated_text

def find_latest_checkpoint(save_dir):
    """Find the latest checkpoint in the save directory."""
    import glob
    checkpoint_pattern = os.path.join(save_dir, "checkpoint_epoch_*")
    checkpoints = glob.glob(checkpoint_pattern)
    if not checkpoints:
        return None

    # Extract epoch numbers and find the latest
    epoch_numbers = []
    for checkpoint in checkpoints:
        try:
            epoch_num = int(checkpoint.split("_epoch_")[-1])
            epoch_numbers.append((epoch_num, checkpoint))
        except ValueError:
            continue

    if epoch_numbers:
        latest_epoch, latest_checkpoint = max(epoch_numbers, key=lambda x: x[0])
        return latest_checkpoint
    return None

def main():
    # Configuration
    model_dir = "./fine_tuned_bloomz_560m"  # Change this to your model directory
    device = torch.device("mps" if torch.backends.mps.is_available() else "cpu")

    # Find the latest checkpoint
    checkpoint_path = find_latest_checkpoint(model_dir)
    if not checkpoint_path:
        print(f"No checkpoints found in {model_dir}")
        return

    print(f"Using checkpoint: {checkpoint_path}")
    
    # Load model
    try:
        model, tokenizer, base_model_info = load_model_from_checkpoint(checkpoint_path)
        print(f"Model loaded from: {checkpoint_path}")
        print(f"Base model: {base_model_info}")
        
        # Example usage
        prompt = "Tweet text : @customer_service This is terrible service Label : "
        print(f"\nInput prompt: {prompt}")
        
        generated_text = generate_text(model, tokenizer, prompt, max_length=150, device=device)
        print(f"Generated text: {generated_text}")
        
        # You can also use the model for classification
        print("\n" + "="*50)
        print("Model is ready for inference!")
        print("You can now use the model for:")
        print("1. Text generation")
        print("2. Classification tasks")
        print("3. Fine-tuning on new data")
        
    except Exception as e:
        print(f"Error loading model: {e}")
        print("Please check that the checkpoint path exists and contains the required files.")

if __name__ == "__main__":
    main()
